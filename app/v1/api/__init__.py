from fastapi import FastAPI

from .users import router as user_router
from .config import router as config_router
from .roles import router as role_router
from .projects import router as project_router
from .jobs import router as jobs_router
from .processes import router as process_router
from .media import router as media_router
from app.v1.webhook.webhook import router as webhook_router

with open("app/v1/api/README.md", "r") as f:
    SUM = f.read()

router = FastAPI(title="Aroma API v1", description=SUM)

router.include_router(user_router)
router.include_router(config_router)
router.include_router(role_router)

router.include_router(project_router)
router.include_router(jobs_router)
router.include_router(process_router)
router.include_router(media_router)
router.include_router(webhook_router)