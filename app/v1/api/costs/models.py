"""
Cost tracking models for the cascading cost system.
Handles cost metadata at job, project, and system levels.
"""

from pydantic import BaseModel, Field, ConfigDict, PlainSerializer
from typing import Annotated, Optional, Dict, List
from datetime import datetime
from bson import ObjectId
from decimal import Decimal


class TokenUsage(BaseModel):
    """Token usage information for API calls"""
    prompt_tokens: int = Field(default=0, description="Number of input/prompt tokens used")
    completion_tokens: int = Field(default=0, description="Number of output/completion tokens used")
    total_tokens: int = Field(default=0, description="Total tokens used (prompt + completion)")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class CostBreakdown(BaseModel):
    """Detailed cost breakdown by process type and model"""
    process_type: str = Field(..., description="Type of process (e.g., generic-entity-extraction)")
    model_name: str = Field(default="gemini-2.0-flash-exp", description="AI model used")
    cost_usd: float = Field(default=0.0, description="Cost in USD")
    cost_nep: float = Field(default=0.0, description="Cost in NEP")
    token_usage: TokenUsage = Field(default_factory=TokenUsage, description="Token usage details")
    api_calls: int = Field(default=0, description="Number of API calls made")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class CostMetadata(BaseModel):
    """Cost metadata for jobs"""
    total_cost_usd: float = Field(default=0.0, description="Total cost in USD")
    total_cost_nep: float = Field(default=0.0, description="Total cost in NEP")
    total_token_usage: TokenUsage = Field(default_factory=TokenUsage, description="Total token usage")
    cost_breakdown: List[CostBreakdown] = Field(default_factory=list, description="Detailed cost breakdown")
    last_calculated: Optional[datetime] = Field(None, description="When costs were last calculated")
    calculation_version: str = Field(default="1.0", description="Version of cost calculation logic used")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class JobCostSummary(BaseModel):
    """Cost summary for a single job"""
    job_id: Annotated[ObjectId, PlainSerializer(str)] = Field(..., description="Job ID")
    job_name: str = Field(..., description="Job name")
    project_id: Annotated[ObjectId, PlainSerializer(str)] = Field(..., description="Project ID")
    status: str = Field(..., description="Job status")
    cost_metadata: CostMetadata = Field(..., description="Cost information")
    created_at: datetime = Field(..., description="Job creation date")
    completed_at: Optional[datetime] = Field(None, description="Job completion date")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class ProjectCostSummary(BaseModel):
    """Cost summary for a project"""
    project_id: Annotated[ObjectId, PlainSerializer(str)] = Field(..., description="Project ID")
    project_name: str = Field(..., description="Project name")
    total_cost_usd: float = Field(default=0.0, description="Total project cost in USD")
    total_cost_nep: float = Field(default=0.0, description="Total project cost in NEP")
    total_jobs: int = Field(default=0, description="Total number of jobs in project")
    completed_jobs: int = Field(default=0, description="Number of completed jobs")
    cost_by_process_type: Dict[str, float] = Field(default_factory=dict, description="Cost breakdown by process type (USD)")
    cost_by_status: Dict[str, float] = Field(default_factory=dict, description="Cost breakdown by job status (USD)")
    total_token_usage: TokenUsage = Field(default_factory=TokenUsage, description="Total token usage")
    last_updated: Optional[datetime] = Field(None, description="When costs were last updated")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class SystemCostSummary(BaseModel):
    """System-wide cost summary"""
    total_cost_usd: float = Field(default=0.0, description="Total system cost in USD")
    total_cost_nep: float = Field(default=0.0, description="Total system cost in NEP")
    total_projects: int = Field(default=0, description="Total number of projects")
    total_jobs: int = Field(default=0, description="Total number of jobs")
    cost_by_process_type: Dict[str, float] = Field(default_factory=dict, description="Cost breakdown by process type (USD)")
    cost_by_project: List[ProjectCostSummary] = Field(default_factory=list, description="Cost breakdown by project")
    total_token_usage: TokenUsage = Field(default_factory=TokenUsage, description="Total token usage")
    last_updated: Optional[datetime] = Field(None, description="When costs were last updated")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class CostReportFilter(BaseModel):
    """Filters for cost reports"""
    start_date: Optional[datetime] = Field(None, description="Start date for cost report")
    end_date: Optional[datetime] = Field(None, description="End date for cost report")
    project_ids: Optional[List[str]] = Field(None, description="Filter by specific project IDs")
    process_types: Optional[List[str]] = Field(None, description="Filter by process types")
    job_statuses: Optional[List[str]] = Field(None, description="Filter by job statuses")
    min_cost_usd: Optional[float] = Field(None, description="Minimum cost threshold in USD")
    max_cost_usd: Optional[float] = Field(None, description="Maximum cost threshold in USD")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class CostReport(BaseModel):
    """Detailed cost report"""
    report_id: str = Field(..., description="Unique report identifier")
    generated_at: datetime = Field(..., description="When the report was generated")
    filters: CostReportFilter = Field(..., description="Filters applied to the report")
    summary: SystemCostSummary = Field(..., description="Overall cost summary")
    job_details: List[JobCostSummary] = Field(default_factory=list, description="Detailed job cost information")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class CostRecalculationRequest(BaseModel):
    """Request model for cost recalculation"""
    scope: str = Field(..., description="Scope of recalculation: 'job', 'project', or 'system'")
    target_id: Optional[str] = Field(None, description="ID of specific job or project to recalculate")
    force_recalculation: bool = Field(default=False, description="Force recalculation even if recently calculated")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class CostRecalculationResponse(BaseModel):
    """Response model for cost recalculation"""
    success: bool = Field(..., description="Whether recalculation was successful")
    message: str = Field(..., description="Status message")
    recalculated_jobs: int = Field(default=0, description="Number of jobs recalculated")
    recalculated_projects: int = Field(default=0, description="Number of projects recalculated")
    total_cost_usd: float = Field(default=0.0, description="Total cost after recalculation")
    execution_time_seconds: float = Field(default=0.0, description="Time taken for recalculation")
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )
