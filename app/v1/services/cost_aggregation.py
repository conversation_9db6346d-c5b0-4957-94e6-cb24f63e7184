"""
Cost aggregation service for cascading cost tracking.
Handles aggregation from job level to project and system levels.
"""

import asyncio
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timezone
from bson import ObjectId
from app.models.user import UserTenantDB
from app.v1.api.costs.models import (
    CostMetadata, TokenUsage, CostBreakdown, 
    ProjectCostSummary, SystemCostSummary, JobCostSummary
)
from app.core.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)


class CostAggregationService:
    """Service for aggregating costs across job, project, and system levels"""
    
    def __init__(self, user_tenant_info: UserTenantDB):
        self.user_tenant_info = user_tenant_info
        self.jobs_collection = user_tenant_info.async_db.jobs
        self.projects_collection = user_tenant_info.async_db.projects
    
    async def calculate_job_cost(self, job_id: str, cost_data: Dict) -> CostMetadata:
        """
        Calculate and store cost metadata for a single job.
        
        Args:
            job_id: ID of the job
            cost_data: Dictionary containing cost information from processing
            
        Returns:
            CostMetadata object with calculated costs
        """
        try:
            # Extract cost information from the processing result
            total_cost_nep = cost_data.get('price_nep', 0.0)
            usage_metadata = cost_data.get('usage_metadata', {})
            
            # Calculate USD cost (NEP to USD conversion)
            total_cost_usd = total_cost_nep / 137.0 if total_cost_nep > 0 else 0.0
            
            # Create token usage
            token_usage = TokenUsage(
                prompt_tokens=usage_metadata.get('prompt_token_count', 0),
                completion_tokens=usage_metadata.get('candidates_token_count', 0),
                total_tokens=usage_metadata.get('prompt_token_count', 0) + usage_metadata.get('candidates_token_count', 0)
            )
            
            # Get job details for process type
            job = await self.jobs_collection.find_one({"_id": ObjectId(job_id)})
            process_type = job.get('process_name', 'unknown') if job else 'unknown'
            
            # Create cost breakdown
            cost_breakdown = [CostBreakdown(
                process_type=process_type,
                model_name=cost_data.get('model_name', 'gemini-2.0-flash-exp'),
                cost_usd=total_cost_usd,
                cost_nep=total_cost_nep,
                token_usage=token_usage,
                api_calls=1
            )]
            
            # Create cost metadata
            cost_metadata = CostMetadata(
                total_cost_usd=total_cost_usd,
                total_cost_nep=total_cost_nep,
                total_token_usage=token_usage,
                cost_breakdown=cost_breakdown,
                last_calculated=datetime.now(timezone.utc),
                calculation_version="1.0"
            )
            
            return cost_metadata
            
        except Exception as e:
            logger.error(f"Error calculating job cost for {job_id}: {str(e)}")
            # Return empty cost metadata on error
            return CostMetadata()
    
    async def update_job_cost_metadata(self, job_id: str, cost_metadata: CostMetadata) -> bool:
        """
        Update job document with cost metadata.
        
        Args:
            job_id: ID of the job to update
            cost_metadata: Cost metadata to store
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            result = await self.jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "cost_metadata": cost_metadata.model_dump(),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating job cost metadata for {job_id}: {str(e)}")
            return False
    
    async def aggregate_project_costs(self, project_id: str) -> ProjectCostSummary:
        """
        Aggregate costs for all jobs in a project.
        
        Args:
            project_id: ID of the project
            
        Returns:
            ProjectCostSummary with aggregated costs
        """
        try:
            # Get project details
            project = await self.projects_collection.find_one({"_id": ObjectId(project_id)})
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            # Get all jobs for this project
            jobs_cursor = self.jobs_collection.find({"project_id": ObjectId(project_id)})
            jobs = await jobs_cursor.to_list(length=None)
            
            # Initialize aggregation variables
            total_cost_usd = 0.0
            total_cost_nep = 0.0
            total_jobs = len(jobs)
            completed_jobs = 0
            cost_by_process_type = {}
            cost_by_status = {}
            total_token_usage = TokenUsage()
            
            # Aggregate costs from all jobs
            for job in jobs:
                cost_metadata = job.get('cost_metadata')
                if cost_metadata:
                    # Add to totals
                    total_cost_usd += cost_metadata.get('total_cost_usd', 0.0)
                    total_cost_nep += cost_metadata.get('total_cost_nep', 0.0)
                    
                    # Add to token usage
                    token_usage = cost_metadata.get('total_token_usage', {})
                    total_token_usage.prompt_tokens += token_usage.get('prompt_tokens', 0)
                    total_token_usage.completion_tokens += token_usage.get('completion_tokens', 0)
                    total_token_usage.total_tokens += token_usage.get('total_tokens', 0)
                    
                    # Aggregate by process type
                    process_type = job.get('process_name', 'unknown')
                    cost_by_process_type[process_type] = cost_by_process_type.get(process_type, 0.0) + cost_metadata.get('total_cost_usd', 0.0)
                
                # Aggregate by status
                status = job.get('status', 'unknown')
                job_cost = cost_metadata.get('total_cost_usd', 0.0) if cost_metadata else 0.0
                cost_by_status[status] = cost_by_status.get(status, 0.0) + job_cost
                
                if status == 'completed':
                    completed_jobs += 1
            
            # Create project cost summary
            project_cost_summary = ProjectCostSummary(
                project_id=ObjectId(project_id),
                project_name=project.get('name', 'Unknown Project'),
                total_cost_usd=total_cost_usd,
                total_cost_nep=total_cost_nep,
                total_jobs=total_jobs,
                completed_jobs=completed_jobs,
                cost_by_process_type=cost_by_process_type,
                cost_by_status=cost_by_status,
                total_token_usage=total_token_usage,
                last_updated=datetime.now(timezone.utc)
            )
            
            return project_cost_summary
            
        except Exception as e:
            logger.error(f"Error aggregating project costs for {project_id}: {str(e)}")
            # Return empty summary on error
            return ProjectCostSummary(
                project_id=ObjectId(project_id),
                project_name="Error",
                last_updated=datetime.now(timezone.utc)
            )
    
    async def aggregate_system_costs(self) -> SystemCostSummary:
        """
        Aggregate costs across all projects in the system.
        
        Returns:
            SystemCostSummary with system-wide cost aggregation
        """
        try:
            # Get all projects
            projects_cursor = self.projects_collection.find({})
            projects = await projects_cursor.to_list(length=None)
            
            # Initialize aggregation variables
            total_cost_usd = 0.0
            total_cost_nep = 0.0
            total_projects = len(projects)
            total_jobs = 0
            cost_by_process_type = {}
            cost_by_project = []
            total_token_usage = TokenUsage()
            
            # Aggregate costs from all projects
            for project in projects:
                project_id = str(project['_id'])
                project_summary = await self.aggregate_project_costs(project_id)
                
                # Add to system totals
                total_cost_usd += project_summary.total_cost_usd
                total_cost_nep += project_summary.total_cost_nep
                total_jobs += project_summary.total_jobs
                
                # Add to token usage
                total_token_usage.prompt_tokens += project_summary.total_token_usage.prompt_tokens
                total_token_usage.completion_tokens += project_summary.total_token_usage.completion_tokens
                total_token_usage.total_tokens += project_summary.total_token_usage.total_tokens
                
                # Aggregate by process type
                for process_type, cost in project_summary.cost_by_process_type.items():
                    cost_by_process_type[process_type] = cost_by_process_type.get(process_type, 0.0) + cost
                
                # Add to project list
                cost_by_project.append(project_summary)
            
            # Create system cost summary
            system_cost_summary = SystemCostSummary(
                total_cost_usd=total_cost_usd,
                total_cost_nep=total_cost_nep,
                total_projects=total_projects,
                total_jobs=total_jobs,
                cost_by_process_type=cost_by_process_type,
                cost_by_project=cost_by_project,
                total_token_usage=total_token_usage,
                last_updated=datetime.now(timezone.utc)
            )
            
            return system_cost_summary
            
        except Exception as e:
            logger.error(f"Error aggregating system costs: {str(e)}")
            # Return empty summary on error
            return SystemCostSummary(last_updated=datetime.now(timezone.utc))
    
    async def recalculate_project_costs(self, project_id: str) -> bool:
        """
        Recalculate costs for a specific project.
        Useful when jobs are added, removed, or modified.
        
        Args:
            project_id: ID of the project to recalculate
            
        Returns:
            True if recalculation was successful, False otherwise
        """
        try:
            # This method can be used to trigger cost recalculation
            # when jobs are modified or deleted
            project_summary = await self.aggregate_project_costs(project_id)
            
            # Optionally store project-level cost summary in database
            # This could be added to projects collection as a cached summary
            
            logger.info(f"Recalculated costs for project {project_id}: ${project_summary.total_cost_usd:.4f}")
            return True
            
        except Exception as e:
            logger.error(f"Error recalculating project costs for {project_id}: {str(e)}")
            return False
